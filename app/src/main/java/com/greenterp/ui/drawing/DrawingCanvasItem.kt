package com.greenterp.ui.drawing

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.greenterp.DrawingCanvas
import com.greenterp.data.DrawingSession

/**
 * 画布项组件
 * 显示单个画布会话
 */
@Composable
fun DrawingCanvasItem(
    session: DrawingSession,
    isCurrentSession: Boolean,
    initialCanvasData: com.greenterp.CanvasData = com.greenterp.CanvasData(),
    onCanvasDataChanged: (com.greenterp.CanvasData) -> Unit = {},
    globalDrawingMode: Boolean,
    onGlobalDrawingModeChanged: (Boolean) -> Unit,
    onLayoutSwapRequested: () -> Unit = {},
    onBackClick: () -> Unit = {},
    onNextClick: () -> Unit = {},
    isDarkTheme: Boolean = false, // 新增：主题参数
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        // 画布区域
        DrawingCanvas(
            sessionId = session.id,
            timestamp = session.timestamp,
            isDrawingMode = globalDrawingMode,
            onDrawingModeChanged = onGlobalDrawingModeChanged,
            onLayoutSwapRequested = onLayoutSwapRequested,
            initialCanvasData = initialCanvasData,
            onCanvasDataChanged = onCanvasDataChanged,
            // Back和Next按钮一直显示，所有会话都传递回调
            onBackClick = onBackClick,
            onNextClick = onNextClick,
            isDarkTheme = isDarkTheme, // 传递主题参数
            modifier = Modifier.fillMaxSize()
        )
    }
}
