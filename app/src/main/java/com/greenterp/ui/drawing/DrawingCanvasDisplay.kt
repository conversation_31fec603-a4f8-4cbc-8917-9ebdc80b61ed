package com.greenterp.ui.drawing

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.greenterp.data.DrawingSession
import kotlinx.coroutines.CoroutineScope

/**
 * 画布显示组件
 * 管理多个画布会话的显示
 */
@Composable
fun DrawingCanvasDisplay(
    sessions: List<DrawingSession>,
    canvasDataCache: Map<String, com.greenterp.CanvasData>,
    onCanvasDataChanged: (String, com.greenterp.CanvasData) -> Unit,
    coroutineScope: CoroutineScope,
    globalDrawingMode: Boolean,
    onGlobalDrawingModeChanged: (Boolean) -> Unit,
    onLayoutSwapRequested: () -> Unit = {},
    onBackClick: () -> Unit = {},
    onNextClick: () -> Unit = {},
    isAutoScrollEnabled: Boolean = true, // 添加自动滚动控制参数
    isDarkTheme: Boolean = false, // 新增：主题参数
    modifier: Modifier = Modifier,
    listState: LazyListState = rememberLazyListState()
) {
    // 当有新的画布会话时，自动滚动到最新的画布（索引0）
    // 只有在自动滚动开启时才执行
    LaunchedEffect(sessions.size, sessions.firstOrNull()?.id, isAutoScrollEnabled) {
        if (sessions.isNotEmpty() && isAutoScrollEnabled) {
            // 使用延迟确保布局完成后再滚动
            kotlinx.coroutines.delay(150)
            listState.animateScrollToItem(0)
            println("DrawingCanvasDisplay: 自动滚动到新画布 ${sessions.first().id}")
        }
    }

    LazyColumn(
        modifier = modifier.background(if (isDarkTheme) Color.Black else Color.White),
        state = listState,
        contentPadding = PaddingValues(0.dp),
        reverseLayout = true, // 反转布局，最新内容在顶部
        userScrollEnabled = true // 始终允许滚动，由各个画布内部控制交互模式
    ) {
        if (sessions.isEmpty()) {
            // 空状态可以不显示任何内容，或者显示提示
            item {
                Box(
                    modifier = Modifier
                        .fillParentMaxSize()
                        .padding(16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "No canvas available\nStart voice recognition to create a new canvas",
                        color = if (isDarkTheme) Color(0xFF999999) else Color(0xFF999999),
                        fontSize = 16.sp,
                        textAlign = TextAlign.Center
                    )
                }
            }
        } else {
            items(
                count = sessions.size,
                key = { index -> sessions[index].id }
            ) { index ->
                val session = sessions[index]
                DrawingCanvasItem(
                    session = session,
                    isCurrentSession = index == 0,
                    initialCanvasData = canvasDataCache[session.id] ?: com.greenterp.CanvasData(),
                    onCanvasDataChanged = { canvasData ->
                        onCanvasDataChanged(session.id, canvasData)
                    },
                    globalDrawingMode = globalDrawingMode,
                    onGlobalDrawingModeChanged = onGlobalDrawingModeChanged,
                    onLayoutSwapRequested = onLayoutSwapRequested,
                    onBackClick = onBackClick,
                    onNextClick = onNextClick,
                    isDarkTheme = isDarkTheme, // 传递主题参数
                    modifier = Modifier
                        .fillParentMaxHeight() // 每个画布区域都占满屏幕高度
                        .padding(horizontal = 0.dp, vertical = 0.dp)
                )
            }
        }
    }
}
