package com.greenterp.ui.settings

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp

import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.greenterp.LanguageConfig

/**
 * 设置对话框组件
 * 包含字体大小、主题、语言等设置选项
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsDialog(
    fontSize: Int,
    onFontSizeChange: (Int) -> Unit,
    isDarkTheme: Boolean,
    onThemeChange: (Boolean) -> Unit,
    highlightColor: Color,
    onHighlightColorClick: () -> Unit,
    primaryLanguage: String,
    onPrimaryLanguageChange: (String) -> Unit,
    secondaryLanguage: String,
    onSecondaryLanguageChange: (String) -> Unit,
    primaryServiceType: String,
    onPrimaryServiceTypeChange: (String) -> Unit,
    secondaryServiceType: String,
    onSecondaryServiceTypeChange: (String) -> Unit,
    // 新增：第三语言相关参数
    tertiaryLanguage: String = "Auto",
    onTertiaryLanguageChange: (String) -> Unit = {},
    tertiaryServiceType: String = "Online-1",
    onTertiaryServiceTypeChange: (String) -> Unit = {},
    isAutoLanguageEnabled: Boolean = false,
    onAutoLanguageEnabledChange: (Boolean) -> Unit = {},
    onDismiss: () -> Unit
) {
    Dialog(
        onDismissRequest = onDismiss
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth(1f)
                .padding(1.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = if (isDarkTheme) Color(0xFF2D2D2D) else Color.White
            )
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                // Title section
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Settings",
                        fontSize = 24.sp,
                        fontWeight = FontWeight.Bold,
                        color = if (isDarkTheme) Color.White else Color.Black
                    )
                    IconButton(
                        onClick = onDismiss,
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            Icons.Default.Close,
                            contentDescription = "Close",
                            tint = Color.Gray
                        )
                    }
                }

                Spacer(modifier = Modifier.height(20.dp))

                // Content section
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(20.dp)
                ) {
                    // 版本信息
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Version  1.0.0",
                            color = if (isDarkTheme) Color(0xFFCCCCCC) else Color.Gray,
                            fontSize = 16.sp
                        )
                    }

                    // 字体大小
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Font Size",
                            color = if (isDarkTheme) Color(0xFFCCCCCC) else Color.Gray,
                            fontSize = 16.sp
                        )
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            OutlinedTextField(
                                value = fontSize.toString(),
                                onValueChange = {
                                    it.toIntOrNull()?.let { size ->
                                        if (size in 12..32) onFontSizeChange(size)
                                    }
                                },
                                modifier = Modifier.width(80.dp),
                                singleLine = true,
                                textStyle = TextStyle(textAlign = TextAlign.Center)
                            )
                            Column {
                                IconButton(
                                    onClick = { if (fontSize < 32) onFontSizeChange(fontSize + 1) },
                                    modifier = Modifier.size(24.dp)
                                ) {
                                    Icon(
                                        Icons.Default.KeyboardArrowUp,
                                        contentDescription = "Increase",
                                        modifier = Modifier.size(16.dp)
                                    )
                                }
                                IconButton(
                                    onClick = { if (fontSize > 12) onFontSizeChange(fontSize - 1) },
                                    modifier = Modifier.size(24.dp)
                                ) {
                                    Icon(
                                        Icons.Default.KeyboardArrowDown,
                                        contentDescription = "Decrease",
                                        modifier = Modifier.size(16.dp)
                                    )
                                }
                            }
                        }
                    }

                    // 主题色控制
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Theme Color",
                            color = if (isDarkTheme) Color(0xFFCCCCCC) else Color.Gray,
                            fontSize = 16.sp
                        )

                        // 主题切换开关
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            // 亮色主题文字
                            Text(
                                text = "☀",
                                fontSize = 18.sp,
                                color = if (!isDarkTheme) Color(0xFF07c160) else Color.Gray
                            )

                            // 切换开关
                            Box(
                                modifier = Modifier
                                    .width(50.dp)
                                    .height(25.dp)
                                    .background(
                                        color = if (isDarkTheme) Color(0xFF07c160) else Color(0xFFE0E0E0),
                                        shape = CircleShape
                                    )
                                    .clickable { onThemeChange(!isDarkTheme) }
                                    .padding(2.dp),
                                contentAlignment = if (isDarkTheme) Alignment.CenterEnd else Alignment.CenterStart
                            ) {
                                Box(
                                    modifier = Modifier
                                        .size(21.dp)
                                        .background(Color.White, CircleShape)
                                )
                            }

                            // 暗色主题文字
                            Text(
                                text = "🌙",
                                fontSize = 18.sp,
                                color = if (isDarkTheme) Color(0xFF07c160) else Color.Gray
                            )
                        }
                    }

                    // 高亮颜色
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Highlight Color",
                            color = if (isDarkTheme) Color(0xFFCCCCCC) else Color.Gray,
                            fontSize = 16.sp
                        )
                        Box(
                            modifier = Modifier
                                .size(32.dp)
                                .background(highlightColor, RoundedCornerShape(4.dp))
                                .border(1.dp, Color.Gray, RoundedCornerShape(4.dp))
                                .clickable { onHighlightColorClick() }
                        )
                    }

                    // 主要语言
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Primary Language",
                            color = if (isDarkTheme) Color(0xFFCCCCCC) else Color.Gray,
                            fontSize = 16.sp,
                            modifier = Modifier.weight(0.3f)
                        )
                        Row(
                            modifier = Modifier.weight(0.7f),
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            // 语言选择下拉框
                            var primaryExpanded by remember { mutableStateOf(false) }
                            val primaryLanguages =
                                LanguageConfig.getLanguageListForService(primaryServiceType)

                            ExposedDropdownMenuBox(
                                expanded = primaryExpanded,
                                onExpandedChange = { primaryExpanded = !primaryExpanded },
                                modifier = Modifier.weight(0.6f)
                            ) {
                                OutlinedTextField(
                                    value = primaryLanguage,
                                    onValueChange = { },
                                    readOnly = true,
                                    trailingIcon = {
                                        ExposedDropdownMenuDefaults.TrailingIcon(expanded = primaryExpanded)
                                    },
                                    modifier = Modifier
                                        .menuAnchor()
                                        .fillMaxWidth(),
                                    textStyle = TextStyle(fontSize = 12.sp)
                                )
                                ExposedDropdownMenu(
                                    expanded = primaryExpanded,
                                    onDismissRequest = { primaryExpanded = false }
                                ) {
                                    primaryLanguages.forEach { languageItem ->
                                        DropdownMenuItem(
                                            text = {
                                                Text(
                                                    text = languageItem.show,
                                                    fontSize = 12.sp
                                                )
                                            },
                                            onClick = {
                                                onPrimaryLanguageChange(languageItem.show)
                                                primaryExpanded = false
                                            }
                                        )
                                    }
                                }
                            }

                            // 在线服务选择下拉框
                            var primaryServiceExpanded by remember { mutableStateOf(false) }
                            val services = LanguageConfig.serviceDisplayNames

                            ExposedDropdownMenuBox(
                                expanded = primaryServiceExpanded,
                                onExpandedChange = {
                                    primaryServiceExpanded = !primaryServiceExpanded
                                },
                                modifier = Modifier.weight(0.5f)
                            ) {
                                OutlinedTextField(
                                    value = primaryServiceType,
                                    onValueChange = { },
                                    readOnly = true,
                                    trailingIcon = {
                                        ExposedDropdownMenuDefaults.TrailingIcon(expanded = primaryServiceExpanded)
                                    },
                                    modifier = Modifier
                                        .menuAnchor()
                                        .fillMaxWidth(),
                                    textStyle = TextStyle(fontSize = 12.sp)
                                )
                                ExposedDropdownMenu(
                                    expanded = primaryServiceExpanded,
                                    onDismissRequest = { primaryServiceExpanded = false }
                                ) {
                                    services.forEach { service ->
                                        DropdownMenuItem(
                                            text = {
                                                Text(
                                                    text = service,
                                                    fontSize = 12.sp
                                                )
                                            },
                                            onClick = {
                                                val oldService = primaryServiceType
                                                onPrimaryServiceTypeChange(service)
                                                primaryServiceExpanded = false

                                                // 当服务改变时，更新语言选项
                                                if (oldService != service) {
                                                    val newLanguageList =
                                                        LanguageConfig.getLanguageListForService(
                                                            service
                                                        )
                                                    if (!newLanguageList.any { it.show == primaryLanguage }) {
                                                        // 如果当前语言在新服务中不可用，选择默认语言
                                                        val defaultLang = when (service) {
                                                            "Online-ZH/EN only" -> newLanguageList[0].show
                                                            else -> newLanguageList.find { it.code == "en-US" }?.show
                                                                ?: "English-United States"
                                                        }
                                                        onPrimaryLanguageChange(defaultLang)
                                                    }
                                                }
                                            }
                                        )
                                    }
                                }
                            }
                        }
                    }

                    // 次要语言
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Secondary Language",
                            color = if (isDarkTheme) Color(0xFFCCCCCC) else Color.Gray,
                            fontSize = 16.sp,
                            modifier = Modifier.weight(0.3f)
                        )
                        Row(
                            modifier = Modifier.weight(0.7f),
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            // 语言选择下拉框
                            var secondaryExpanded by remember { mutableStateOf(false) }
                            val secondaryLanguages =
                                LanguageConfig.getLanguageListForService(secondaryServiceType)

                            ExposedDropdownMenuBox(
                                expanded = secondaryExpanded,
                                onExpandedChange = { secondaryExpanded = !secondaryExpanded },
                                modifier = Modifier.weight(0.6f)
                            ) {
                                OutlinedTextField(
                                    value = secondaryLanguage,
                                    onValueChange = { },
                                    readOnly = true,
                                    trailingIcon = {
                                        ExposedDropdownMenuDefaults.TrailingIcon(expanded = secondaryExpanded)
                                    },
                                    modifier = Modifier
                                        .menuAnchor()
                                        .fillMaxWidth(),
                                    textStyle = TextStyle(fontSize = 12.sp)
                                )
                                ExposedDropdownMenu(
                                    expanded = secondaryExpanded,
                                    onDismissRequest = { secondaryExpanded = false }
                                ) {
                                    secondaryLanguages.forEach { languageItem ->
                                        DropdownMenuItem(
                                            text = {
                                                Text(
                                                    text = languageItem.show,
                                                    fontSize = 12.sp
                                                )
                                            },
                                            onClick = {
                                                onSecondaryLanguageChange(languageItem.show)
                                                secondaryExpanded = false
                                            }
                                        )
                                    }
                                }
                            }

                            // 在线服务选择下拉框
                            var secondaryServiceExpanded by remember { mutableStateOf(false) }
                            val services = LanguageConfig.serviceDisplayNames

                            ExposedDropdownMenuBox(
                                expanded = secondaryServiceExpanded,
                                onExpandedChange = {
                                    secondaryServiceExpanded = !secondaryServiceExpanded
                                },
                                modifier = Modifier.weight(0.5f)
                            ) {
                                OutlinedTextField(
                                    value = secondaryServiceType,
                                    onValueChange = { },
                                    readOnly = true,
                                    trailingIcon = {
                                        ExposedDropdownMenuDefaults.TrailingIcon(expanded = secondaryServiceExpanded)
                                    },
                                    modifier = Modifier
                                        .menuAnchor()
                                        .fillMaxWidth(),
                                    textStyle = TextStyle(fontSize = 12.sp)
                                )
                                ExposedDropdownMenu(
                                    expanded = secondaryServiceExpanded,
                                    onDismissRequest = { secondaryServiceExpanded = false }
                                ) {
                                    services.forEach { service ->
                                        DropdownMenuItem(
                                            text = {
                                                Text(
                                                    text = service,
                                                    fontSize = 12.sp
                                                )
                                            },
                                            onClick = {
                                                val oldService = secondaryServiceType
                                                onSecondaryServiceTypeChange(service)
                                                secondaryServiceExpanded = false

                                                // 当服务改变时，更新语言选项
                                                if (oldService != service) {
                                                    val newLanguageList =
                                                        LanguageConfig.getLanguageListForService(
                                                            service
                                                        )
                                                    if (!newLanguageList.any { it.show == secondaryLanguage }) {
                                                        // 如果当前语言在新服务中不可用，选择默认语言
                                                        val defaultLang = when (service) {
                                                            "Online-ZH/EN only" -> newLanguageList.find { it.code == "zh-CN" }?.show
                                                                ?: "Chinese-Mandarin"

                                                            else -> newLanguageList.find { it.code == "zh-CN" }?.show
                                                                ?: "Chinese-Mandarin"
                                                        }
                                                        onSecondaryLanguageChange(defaultLang)
                                                    }
                                                }
                                            }
                                        )
                                    }
                                }
                            }
                        }
                    }

                    // 第三语言 (Tertiary Language)
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Tertiary Language",
                            color = if (isDarkTheme) Color(0xFFCCCCCC) else Color.Gray,
                            fontSize = 16.sp,
                            modifier = Modifier.weight(0.3f)
                        )
                        Row(
                            modifier = Modifier.weight(0.7f),
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 语言选择下拉框（固定为Auto）
                            var tertiaryExpanded by remember { mutableStateOf(false) }
                            val tertiaryLanguages = LanguageConfig.getTertiaryLanguageList()

                            ExposedDropdownMenuBox(
                                expanded = tertiaryExpanded,
                                onExpandedChange = { tertiaryExpanded = !tertiaryExpanded },
                                modifier = Modifier.weight(0.6f)
                            ) {
                                OutlinedTextField(
                                    value = tertiaryLanguage,
                                    onValueChange = { },
                                    readOnly = true,
                                    trailingIcon = {
                                        ExposedDropdownMenuDefaults.TrailingIcon(expanded = tertiaryExpanded)
                                    },
                                    modifier = Modifier
                                        .menuAnchor()
                                        .fillMaxWidth(),
                                    textStyle = TextStyle(fontSize = 12.sp)
                                )
                                ExposedDropdownMenu(
                                    expanded = tertiaryExpanded,
                                    onDismissRequest = { tertiaryExpanded = false }
                                ) {
                                    tertiaryLanguages.forEach { languageItem ->
                                        DropdownMenuItem(
                                            text = {
                                                Text(
                                                    text = languageItem.show,
                                                    fontSize = 12.sp
                                                )
                                            },
                                            onClick = {
                                                onTertiaryLanguageChange(languageItem.show)
                                                tertiaryExpanded = false
                                            }
                                        )
                                    }
                                }
                            }

                            // 在线服务选择下拉框
                            var tertiaryServiceExpanded by remember { mutableStateOf(false) }
                            val services = LanguageConfig.serviceDisplayNames

                            ExposedDropdownMenuBox(
                                expanded = tertiaryServiceExpanded,
                                onExpandedChange = {
                                    tertiaryServiceExpanded = !tertiaryServiceExpanded
                                },
                                modifier = Modifier.weight(0.4f)
                            ) {
                                OutlinedTextField(
                                    value = tertiaryServiceType,
                                    onValueChange = { },
                                    readOnly = true,
                                    trailingIcon = {
                                        ExposedDropdownMenuDefaults.TrailingIcon(expanded = tertiaryServiceExpanded)
                                    },
                                    modifier = Modifier
                                        .menuAnchor()
                                        .fillMaxWidth(),
                                    textStyle = TextStyle(fontSize = 12.sp)
                                )
                                ExposedDropdownMenu(
                                    expanded = tertiaryServiceExpanded,
                                    onDismissRequest = { tertiaryServiceExpanded = false }
                                ) {
                                    services.forEach { service ->
                                        DropdownMenuItem(
                                            text = {
                                                Text(
                                                    text = service,
                                                    fontSize = 12.sp
                                                )
                                            },
                                            onClick = {
                                                onTertiaryServiceTypeChange(service)
                                                tertiaryServiceExpanded = false
                                            }
                                        )
                                    }
                                }
                            }

                            // Auto语言开关
                            Switch(
                                checked = isAutoLanguageEnabled,
                                onCheckedChange = onAutoLanguageEnabledChange,
                                modifier = Modifier.scale(0.8f)
                            )
                        }
                    }
                }
            }
        }
    }
}
