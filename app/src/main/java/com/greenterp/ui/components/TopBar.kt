package com.greenterp.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.boundsInRoot
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.greenterp.R
import com.greenterp.network.ApiService
import com.greenterp.network.TranslationModel
import com.greenterp.network.TranslationResult

/**
 * 顶部导航栏组件
 * 包含Logo、翻译模型选择、搜索框、用户信息和功能按钮
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TopBar(
    searchValue: String = "",
    onSearchValueChange: (String) -> Unit = {},
    translationModels: List<TranslationModel> = emptyList(),
    selectedTranslationModel: TranslationModel? = null,
    onTranslationModelSelected: (TranslationModel) -> Unit = {},
    onSearchSubmit: (String) -> Unit = {},
    onDrawingClick: () -> Unit = {},
    onLogoutClick: () -> Unit = {},
    showTranslationPanel: Boolean = false,
    translationResult: TranslationResult? = null,
    primaryLanguage: String = "English-United States",
    secondaryLanguage: String = "Chinese-Mandarin",
    currentLanguageIsPrimary: Boolean = true,
    onCloseTranslationPanel: () -> Unit = {},
    onClearFocus: () -> Unit = {},
    userEmail: String = "",
    isDarkTheme: Boolean = false
) {
    val apiService = remember { ApiService.getInstance() }
    val searchFocusRequester = remember { FocusRequester() }
    var searchBoxBounds by remember { mutableStateOf<androidx.compose.ui.geometry.Rect?>(null) }

    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .statusBarsPadding() // 为状态栏留出空间
            .pointerInput(showTranslationPanel) {
                detectTapGestures(
                    onTap = {
                        // 点击TopBar的空白区域时关闭翻译面板和键盘焦点
                        if (showTranslationPanel) {
                            onCloseTranslationPanel()
                        }
                        onClearFocus()
                    }
                )
            },
        color = if (isDarkTheme) Color(0xFF2D2D2D) else Color.White
    ) {
        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 左侧 Logo 和标题
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.gt),
                        contentDescription = "Logo",
                        modifier = Modifier
                            .size(32.dp)
                            .padding(end = 8.dp),
                        contentScale = ContentScale.Fit
                    )
                    Text(
                        text = "TerpMate",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF07c160),
                        style = TextStyle(
                            fontFamily = FontFamily.Default,
                            fontStyle = FontStyle.Normal
                        )
                    )
                }

                // 翻译模型选择下拉框
                var modelDropdownExpanded by remember { mutableStateOf(false) }

                ExposedDropdownMenuBox(
                    expanded = modelDropdownExpanded,
                    onExpandedChange = { modelDropdownExpanded = it },
                    modifier = Modifier.padding(start = 12.dp)
                ) {
                    OutlinedTextField(
                        value = selectedTranslationModel?.name ?: "Select Model",
                        onValueChange = { },
                        readOnly = true,
                        modifier = Modifier
                            .width(120.dp)
                            .height(48.dp)
                            .menuAnchor(),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = Color(0xFF07c160),
                            unfocusedBorderColor = Color(0xFFE0E0E0)
                        ),
                        shape = RoundedCornerShape(20.dp),
                        singleLine = true,
                        textStyle = TextStyle(fontSize = 12.sp),
                        trailingIcon = {
                            ExposedDropdownMenuDefaults.TrailingIcon(expanded = modelDropdownExpanded)
                        }
                    )

                    ExposedDropdownMenu(
                        expanded = modelDropdownExpanded,
                        onDismissRequest = { modelDropdownExpanded = false }
                    ) {
                        translationModels.forEach { model ->
                            DropdownMenuItem(
                                text = {
                                    Text(
                                        text = model.name,
                                        fontSize = 12.sp
                                    )
                                },
                                onClick = {
                                    onTranslationModelSelected(model)
                                    modelDropdownExpanded = false
                                }
                            )
                        }
                    }
                }

                // 中间查词输入框和翻译面板容器
                Column(
                    modifier = Modifier
                        .padding(start = 12.dp)
                        .weight(1f)  // 占用中间剩余空间
                ) {
                    // 搜索框
                    ModernSearchBox(
                        searchValue = searchValue,
                        onSearchValueChange = onSearchValueChange,
                        onSearchSubmit = onSearchSubmit,
                        modifier = Modifier
                            .fillMaxWidth()
                            .onGloballyPositioned { coordinates ->
                                searchBoxBounds = coordinates.boundsInRoot()
                            },
                        focusRequester = searchFocusRequester,
                        isDarkTheme = isDarkTheme
                    )
                }

                // 右侧用户信息和功能按钮
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.End,
                    modifier = Modifier.padding(start = 12.dp)
                ) {
                    Text(
                        text = userEmail.ifEmpty { "未登录" },
                        fontSize = 14.sp,
                        color = if (isDarkTheme) Color(0xFFCCCCCC) else Color(0xFF666666),
                        modifier = Modifier.padding(end = 12.dp)
                    )


                    // 画笔按钮
                    IconButton(
                        onClick = onDrawingClick,
                        modifier = Modifier
                            .size(32.dp)
                            .padding(end = 8.dp)
                    ) {
                        Icon(
                            Icons.Default.Edit,
                            contentDescription = "Drawing",
                            tint = Color(0xFF07c160),
                            modifier = Modifier.size(20.dp)
                        )
                    }

                    Button(
                        onClick = onLogoutClick,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF07c160)
                        ),
                        modifier = Modifier.height(32.dp),
                        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 4.dp)
                    ) {
                        Text(
                            text = "Logout",
                            fontSize = 12.sp,
                            color = Color.White
                        )
                    }
                }
            }

        }

        // 悬浮翻译面板
        TranslationPopover(
            isVisible = showTranslationPanel,
            translationResult = translationResult,
            primaryLanguage = primaryLanguage,
            secondaryLanguage = secondaryLanguage,
            currentLanguageIsPrimary = currentLanguageIsPrimary,
            searchBoxBounds = searchBoxBounds,
            onDismiss = onCloseTranslationPanel
        )
    }
}
