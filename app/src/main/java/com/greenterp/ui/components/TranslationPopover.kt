package com.greenterp.ui.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Star
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.greenterp.R
import android.widget.Toast
import com.greenterp.network.ApiService
import com.greenterp.network.TranslationResult
import com.greenterp.utils.SettingsManager
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

/**
 * 翻译结果弹出框组件
 * 显示翻译结果的悬浮面板
 */
@Composable
fun TranslationPopover(
    isVisible: Boolean,
    translationResult: TranslationResult?,
    primaryLanguage: String,
    secondaryLanguage: String,
    currentLanguageIsPrimary: Boolean,
    searchBoxBounds: androidx.compose.ui.geometry.Rect?,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val apiService = remember { ApiService.getInstance() }
    val settingsManager = remember { SettingsManager(context) }

    // 收藏状态
    var isFavorited by remember { mutableStateOf(false) }
    var isProcessingFavorite by remember { mutableStateOf(false) }

    // Force Replace弹框状态
    var showForceReplaceDialog by remember { mutableStateOf(false) }
    var replaceText by remember { mutableStateOf("") }
    var isProcessingReplace by remember { mutableStateOf(false) }

    // 当翻译结果改变时重置收藏状态
    LaunchedEffect(translationResult) {
        isFavorited = false
        isProcessingFavorite = false
        showForceReplaceDialog = false
        replaceText = ""
        isProcessingReplace = false
    }

    // 收藏功能实现
    val handleFavorite = {
        if (!isProcessingFavorite && translationResult is TranslationResult.Success) {
            isProcessingFavorite = true
            coroutineScope.launch {
                try {
                    val originalText = translationResult.response.data?.originalText ?: ""
                    val translatedText = translationResult.response.data?.translatedText ?: ""
                    val userId = apiService.getCurrentUserId()

                    if (userId.isEmpty()) {
                        Toast.makeText(context, "User not logged in", Toast.LENGTH_SHORT).show()
                        return@launch
                    }

                    val currentSceneId = settingsManager.selectedVocabularyId

                    if (currentSceneId == -1) {
                        // 场景为空，先创建场景
                        val currentDate = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())
                        val createSceneResult = apiService.saveVocabulary(currentDate, userId)

                        when (createSceneResult) {
                            is com.greenterp.data.VocabularySaveResult.Success -> {
                                val newSceneId = createSceneResult.vocabulary.id
                                // 保存新场景到设置
                                settingsManager.selectedVocabularyId = newSceneId
                                settingsManager.selectedVocabularyName = currentDate

                                // 使用新场景ID保存词汇
                                val saveResult = apiService.saveVocabularyManagement(
                                    glossaryA = originalText,
                                    glossaryB = translatedText,
                                    sceneId = newSceneId,
                                    userId = userId
                                )

                                when (saveResult) {
                                    is com.greenterp.data.VocabularyManagementSaveResult.Success -> {
                                        isFavorited = true
                                        Toast.makeText(context, "Favorite added successfully", Toast.LENGTH_SHORT).show()
                                    }
                                    is com.greenterp.data.VocabularyManagementSaveResult.Error -> {
                                        Toast.makeText(context, "Failed to add favorite: ${saveResult.message}", Toast.LENGTH_SHORT).show()
                                    }
                                    else -> {}
                                }
                            }
                            is com.greenterp.data.VocabularySaveResult.Error -> {
                                Toast.makeText(context, "Failed to create scene: ${createSceneResult.message}", Toast.LENGTH_SHORT).show()
                            }
                            else -> {}
                        }
                    } else {
                        // 场景不为空，直接保存词汇
                        val saveResult = apiService.saveVocabularyManagement(
                            glossaryA = originalText,
                            glossaryB = translatedText,
                            sceneId = currentSceneId,
                            userId = userId
                        )

                        when (saveResult) {
                            is com.greenterp.data.VocabularyManagementSaveResult.Success -> {
                                isFavorited = true
                                Toast.makeText(context, "Favorite added successfully", Toast.LENGTH_SHORT).show()
                            }
                            is com.greenterp.data.VocabularyManagementSaveResult.Error -> {
                                Toast.makeText(context, "Failed to add favorite: ${saveResult.message}", Toast.LENGTH_SHORT).show()
                            }
                            else -> {}
                        }
                    }
                } catch (e: Exception) {
                    Toast.makeText(context, "Favorite error: ${e.message}", Toast.LENGTH_SHORT).show()
                } finally {
                    isProcessingFavorite = false
                }
            }
        }
    }

    // Force Replace功能实现
    val handleForceReplace = {
        if (!isProcessingReplace && translationResult is TranslationResult.Success && replaceText.isNotEmpty()) {
            isProcessingReplace = true
            coroutineScope.launch {
                try {
                    val originalText = translationResult.response.data?.originalText ?: ""
                    val userId = apiService.getCurrentUserId()

                    if (userId.isEmpty()) {
                        Toast.makeText(context, "User not logged in", Toast.LENGTH_SHORT).show()
                        return@launch
                    }

                    val currentSceneId = settingsManager.selectedReplaceId

                    if (currentSceneId == -1) {
                        // 场景为空，先创建场景
                        val currentDate = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())
                        val createSceneResult = apiService.saveReplaceScene(currentDate, userId)

                        when (createSceneResult) {
                            is com.greenterp.data.ReplaceSceneSaveResult.Success -> {
                                val newSceneId = createSceneResult.replaceScene.id
                                // 保存新场景到设置
                                settingsManager.selectedReplaceId = newSceneId
                                settingsManager.selectedReplaceName = currentDate

                                // 使用新场景ID保存替换
                                val saveResult = apiService.saveReplaceManagement(
                                    replaceA = originalText,
                                    replaceB = replaceText,
                                    sceneId = newSceneId,
                                    userId = userId
                                )

                                when (saveResult) {
                                    is com.greenterp.data.ReplaceManagementSaveResult.Success -> {
                                        Toast.makeText(context, "Replace added successfully", Toast.LENGTH_SHORT).show()
                                        showForceReplaceDialog = false
                                        replaceText = ""
                                    }
                                    is com.greenterp.data.ReplaceManagementSaveResult.Error -> {
                                        Toast.makeText(context, "Failed to add replace: ${saveResult.message}", Toast.LENGTH_SHORT).show()
                                    }
                                    else -> {}
                                }
                            }
                            is com.greenterp.data.ReplaceSceneSaveResult.Error -> {
                                Toast.makeText(context, "Failed to create scene: ${createSceneResult.message}", Toast.LENGTH_SHORT).show()
                            }
                            else -> {}
                        }
                    } else {
                        // 场景不为空，直接保存替换
                        val saveResult = apiService.saveReplaceManagement(
                            replaceA = originalText,
                            replaceB = replaceText,
                            sceneId = currentSceneId,
                            userId = userId
                        )

                        when (saveResult) {
                            is com.greenterp.data.ReplaceManagementSaveResult.Success -> {
                                Toast.makeText(context, "Replace added successfully", Toast.LENGTH_SHORT).show()
                                showForceReplaceDialog = false
                                replaceText = ""
                            }
                            is com.greenterp.data.ReplaceManagementSaveResult.Error -> {
                                Toast.makeText(context, "Failed to add replace: ${saveResult.message}", Toast.LENGTH_SHORT).show()
                            }
                            else -> {}
                        }
                    }
                } catch (e: Exception) {
                    Toast.makeText(context, "Replace error: ${e.message}", Toast.LENGTH_SHORT).show()
                } finally {
                    isProcessingReplace = false
                }
            }
        }
    }

    if (isVisible && searchBoxBounds != null) {
        val density = LocalDensity.current

        // 使用 Popup 创建悬浮层
        androidx.compose.ui.window.Popup(
            alignment = Alignment.TopStart,
            offset = IntOffset(
                x = searchBoxBounds.left.toInt(),
                y = searchBoxBounds.bottom.toInt() // 搜索框底部紧贴
            ),
            onDismissRequest = onDismiss
        ) {
            Card(
                modifier = Modifier
                    .width((searchBoxBounds.width / density.density).dp)
                    .pointerInput(Unit) {
                        detectTapGestures(
                            onTap = {
                                // 点击翻译面板时不传播事件，防止关闭面板
                            }
                        )
                    },
                shape = RoundedCornerShape(8.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
                colors = CardDefaults.cardColors(containerColor = Color.White)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    // 语言方向显示 - 根据当前活动语言确定翻译方向
                    val (sourceLanguage, targetLanguage) = if (currentLanguageIsPrimary) {
                        primaryLanguage to secondaryLanguage
                    } else {
                        secondaryLanguage to primaryLanguage
                    }

                    Text(
                        text = "${sourceLanguage.replace("-", " - ")} > ${targetLanguage.replace("-", " - ")}",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF666666),
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 12.dp),
                        textAlign = TextAlign.Center
                    )

                    // 翻译结果显示
                    when (translationResult) {
                        is TranslationResult.Loading -> {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Center
                            ) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(16.dp),
                                    color = Color(0xFF07c160),
                                    strokeWidth = 2.dp
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = "Translating...",
                                    fontSize = 14.sp,
                                    color = Color(0xFF666666)
                                )
                            }
                        }
                        is TranslationResult.Success -> {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 8.dp, horizontal = 12.dp)
                            ) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    // 收藏按钮
                                    IconButton(
                                        onClick = handleFavorite,
                                        enabled = !isProcessingFavorite,
                                        modifier = Modifier.size(48.dp)
                                    ) {
                                        if (isProcessingFavorite) {
                                            CircularProgressIndicator(
                                                modifier = Modifier.size(16.dp),
                                                color = Color(0xFFFFD700),
                                                strokeWidth = 2.dp
                                            )
                                        } else {
                                            if (isFavorited) {
                                                Icon(
                                                    imageVector = Icons.Default.Star,
                                                    contentDescription = "Favorited",
                                                    tint = Color(0xFFFFD700),
                                                    modifier = Modifier.size(20.dp)
                                                )
                                            } else {
                                                Icon(
                                                    painter = painterResource(id = R.drawable.star_24px),
                                                    contentDescription = "Add to Favorites",
                                                    tint = Color(0xFFFFD700),
                                                    modifier = Modifier.size(20.dp)
                                                )
                                            }
                                        }
                                    }

                                    // 原文 + 更多按钮放一个 Row 中
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        modifier = Modifier
                                            .padding(start = 4.dp)
                                            .wrapContentWidth()  // 原文和按钮自然宽度排布
                                    ) {
                                        // 原文文本
                                        Text(
                                            text = translationResult.response.data?.originalText ?: "",
                                            fontSize = 14.sp,
                                            color = Color.Black,
                                            fontWeight = FontWeight.Bold,
                                            style = TextStyle(
                                                fontFamily = FontFamily.Default,
                                                fontStyle = FontStyle.Normal
                                            )
                                        )

                                        Spacer(modifier = Modifier.width(8.dp))  // 原文与按钮之间的间距

                                        // 更多翻译按钮
                                        IconButton(
                                            onClick = {
                                                showForceReplaceDialog = true
                                                replaceText = ""
                                            },
                                            modifier = Modifier.size(36.dp)
                                        ) {
                                            Icon(
                                                painter = painterResource(id = R.drawable.replace_24px),
                                                contentDescription = "Force Replace",
                                                tint = Color(0xFF666666),
                                                modifier = Modifier.size(20.dp)
                                            )
                                        }
                                    }
                                }

                                // 第二行：译文文本，对齐原文起始位置（48.dp 为收藏按钮宽度）
                                Text(
                                    text = translationResult.response.data?.translatedText ?: "",
                                    fontSize = 14.sp,
                                    color = Color(0xFF666666),
                                    modifier = Modifier
                                        .padding(start = 48.dp + 4.dp)  // 与原文左对齐
                                )
                            }

                        }
                        is TranslationResult.Error -> {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Center
                            ) {
                                Icon(
                                    Icons.Default.Warning,
                                    contentDescription = "Error",
                                    tint = Color(0xFFFF5722),
                                    modifier = Modifier.size(16.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = translationResult.message,
                                    fontSize = 12.sp,
                                    color = Color(0xFFFF5722)
                                )
                            }
                        }
                        null -> {
                            // 空状态，不显示任何内容
                        }
                    }
                }
            }
        }
    }

    // Force Replace弹框
    if (showForceReplaceDialog && translationResult is TranslationResult.Success) {
        Dialog(
            onDismissRequest = {
                showForceReplaceDialog = false
                replaceText = ""
            },
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true
            )
        ) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(containerColor = Color.White)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(24.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // 标题栏
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Force Replace",
                            fontSize = 20.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color.Black
                        )

                        IconButton(
                            onClick = {
                                showForceReplaceDialog = false
                                replaceText = ""
                            },
                            modifier = Modifier.size(24.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Close,
                                contentDescription = "Close",
                                tint = Color.Gray,
                                modifier = Modifier.size(20.dp)
                            )
                        }
                    }

                    // 源文本和输入框
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        // 源文本
                        Text(
                            text = translationResult.response.data?.originalText ?: "hello",
                            fontSize = 16.sp,
                            color = Color.Black,
                            modifier = Modifier.weight(0.3f)
                        )

                        // 箭头
                        Text(
                            text = "→",
                            fontSize = 18.sp,
                            color = Color.Gray
                        )

                        // 输入框
                        OutlinedTextField(
                            value = replaceText,
                            onValueChange = { replaceText = it },
                            placeholder = {
                                Text(
                                    text = "",
                                    color = Color.Gray
                                )
                            },
                            modifier = Modifier.weight(0.7f),
                            singleLine = true,
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedBorderColor = Color(0xFF07c160),
                                unfocusedBorderColor = Color(0xFFE0E0E0)
                            ),
                            shape = RoundedCornerShape(8.dp)
                        )
                    }

                    // 按钮行
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        // Cancel按钮
                        OutlinedButton(
                            onClick = {
                                showForceReplaceDialog = false
                                replaceText = ""
                            },
                            modifier = Modifier.weight(1f),
                            colors = ButtonDefaults.outlinedButtonColors(
                                contentColor = Color.Gray
                            ),
                            border = BorderStroke(1.dp, Color.Gray),
                            shape = RoundedCornerShape(8.dp)
                        ) {
                            Text(
                                text = "Cancel",
                                fontSize = 16.sp
                            )
                        }

                        // Confirm按钮
                        Button(
                            onClick = handleForceReplace,
                            enabled = !isProcessingReplace && replaceText.isNotEmpty(),
                            modifier = Modifier.weight(1f),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF07c160)
                            ),
                            shape = RoundedCornerShape(8.dp)
                        ) {
                            if (isProcessingReplace) {
                                Row(
                                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    CircularProgressIndicator(
                                        modifier = Modifier.size(16.dp),
                                        color = Color.White,
                                        strokeWidth = 2.dp
                                    )
                                    Text(
                                        text = "Processing...",
                                        fontSize = 16.sp,
                                        color = Color.White
                                    )
                                }
                            } else {
                                Text(
                                    text = "Confirm",
                                    fontSize = 16.sp,
                                    color = Color.White
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}
