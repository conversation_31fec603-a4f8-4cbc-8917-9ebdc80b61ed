package com.greenterp

import android.content.Context
import android.content.pm.PackageManager
import android.util.Log
import androidx.core.content.ContextCompat
import com.greenterp.data.RecognitionResult
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * Azure语音识别管理器
 * 管理Azure语音识别的状态和结果
 */
class AzureAsrManager(private val context: Context) {
    
    companion object {
        private const val TAG = "AzureAsrManager"
    }
    
    private val asrService = AzureAsrService()
    
    // 识别状态
    private val _isRecording = MutableStateFlow(false)
    val isRecording: StateFlow<Boolean> = _isRecording.asStateFlow()
    
    // 当前会话ID
    private val _currentSessionId = MutableStateFlow<String?>(null)
    val currentSessionId: StateFlow<String?> = _currentSessionId.asStateFlow()
    
    // 识别结果
    private val _recognitionResults = MutableStateFlow<List<RecognitionResult>>(emptyList())
    val recognitionResults: StateFlow<List<RecognitionResult>> = _recognitionResults.asStateFlow()

    // 当前结果索引计数器
    private var currentResultIndex = 0
    
    // 错误信息
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    // 连接状态
    private val _isConnected = MutableStateFlow(false)
    val isConnected: StateFlow<Boolean> = _isConnected.asStateFlow()
    
    // 当前语言代码列表（支持多语言识别）
    private var currentLanguageCodes: List<String> = listOf("en-US", "zh-CN")
    
    private val asrCallback = object : AzureAsrService.AsrCallback {
        override fun onConnected(sessionId: String) {
            Log.d(TAG, "Azure ASR连接成功: $sessionId")
            _currentSessionId.value = sessionId
            _isConnected.value = true
            _isRecording.value = true  // 在真正连接成功时才设置录音状态
            _errorMessage.value = null
        }

        override fun onIntermediateResult(text: String, index: Int, languageCode:String) {
            Log.d(TAG, "Azure ASR中间结果: $text (index: $index) language $languageCode")
            val currentResults = _recognitionResults.value.toMutableList()

            // 查找是否已存在相同索引的中间结果
            val existingIndex = currentResults.indexOfFirst { it.index == index && it.isIntermediate }

            val intermediateResult = RecognitionResult(
                transcript = text,
                isIntermediate = true,
                index = index,
                source = "azure",
                languageCode = languageCode
            )

            if (existingIndex >= 0) {
                // 替换现有的中间结果
                currentResults[existingIndex] = intermediateResult
            } else {
                // 添加新的中间结果
                currentResults.add(intermediateResult)
            }

            _recognitionResults.value = currentResults
        }

        override fun onFinalResult(text: String, index: Int,languageCode: String) {
            Log.d(TAG, "Azure ASR最终结果: $text (index: $index) language $languageCode")
            val currentResults = _recognitionResults.value.toMutableList()

            // 查找并替换对应的中间结果
            val existingIndex = currentResults.indexOfFirst { it.index == index }

            val finalResult = RecognitionResult(
                transcript = text,
                isIntermediate = false,
                index = index,
                source = "azure",
                languageCode = languageCode
            )

            if (existingIndex >= 0) {
                // 替换现有结果（中间结果或之前的最终结果）
                currentResults[existingIndex] = finalResult
            } else {
                // 添加新的最终结果
                currentResults.add(finalResult)
            }

            _recognitionResults.value = currentResults
        }

        override fun onError(code: String, message: String) {
            Log.e(TAG, "Azure ASR错误: $code - $message")
            _errorMessage.value = "$code: $message"
            _isRecording.value = false
            _isConnected.value = false
        }

        override fun onDisconnected() {
            Log.d(TAG, "Azure ASR连接断开")
            _isConnected.value = false
            _isRecording.value = false
            _currentSessionId.value = null
        }

        override fun onStopCompleted() {
            Log.d(TAG, "Azure ASR停止完成")
            // 可以在这里添加停止完成的处理逻辑
        }
    }
    
    /**
     * 检查录音权限
     */
    fun hasRecordPermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            android.Manifest.permission.RECORD_AUDIO
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 设置语言（支持多语言）
     */
    fun setLanguages(languageCodes: List<String>) {
        this.currentLanguageCodes = languageCodes
        Log.d(TAG, "设置Azure ASR多语言: ${languageCodes.joinToString(", ")}")
    }

    /**
     * 设置单个语言（向后兼容）
     */
    fun setLanguage(languageCode: String) {
        this.currentLanguageCodes = listOf(languageCode)
        Log.d(TAG, "设置Azure ASR单语言: $languageCode")
    }
    
    /**
     * 开始语音识别
     */
    fun startRecognition(): Boolean {
        if (!hasRecordPermission()) {
            _errorMessage.value = "Recording permission required"
            return false
        }

        if (_isRecording.value) {
            Log.w(TAG, "已经在录音中")
            return false
        }

        // 清空之前的结果
        _recognitionResults.value = emptyList()
        _errorMessage.value = null
        // 注意：不要在这里设置 _isRecording.value = true
        // 等待Azure服务真正连接成功后再设置

        // 重置结果索引计数器
        currentResultIndex = 0

        // 开始识别（传递语言列表）
        asrService.startRecognition(currentLanguageCodes, asrCallback)

        return true
    }
    
    /**
     * 停止语音识别
     */
    fun stopRecognition() {
        if (!_isRecording.value) {
            Log.w(TAG, "没有在录音")
            return
        }
        
        _isRecording.value = false
        asrService.stopRecognition()
    }
    
    /**
     * 清空识别结果
     */
    fun clearResults() {
        _recognitionResults.value = emptyList()
    }
    
    /**
     * 清空错误信息
     */
    fun clearError() {
        _errorMessage.value = null
    }
    
    /**
     * 释放资源
     */
    fun release() {
        // 先停止识别（这个操作是异步的，不会阻塞）
        if (_isRecording.value) {
            stopRecognition()
        }

        // 异步释放服务资源，避免阻塞主线程
        asrService.release()

        Log.d(TAG, "Azure ASR管理器资源已释放")
    }
}
