package com.greenterp

import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import android.util.Log
import com.google.gson.Gson
import com.google.gson.JsonObject
import kotlinx.coroutines.*
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.IOException
import java.util.concurrent.TimeUnit

// ASR结果数据类
data class AsrResultData(
    val cn: CnData
)

data class CnData(
    val st: StData
)

data class StData(
    val rt: List<RtData>,
    val type: String
)

data class RtData(
    val ws: List<WsData>
)

data class WsData(
    val cw: List<CwData>
)

data class CwData(
    val w: String
)

/**
 * 讯飞Online-1语音识别服务
 */
class XfeiAsrService {
    
    companion object {
        private const val TAG = "XfeiAsrService"
        private const val TOKEN_URL = "http://117.72.99.27:15005/getAsrToken"
        private const val SAMPLE_RATE = 16000
        private const val CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO
        private const val AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT
        private const val BUFFER_SIZE_FACTOR = 2
        private const val AUDIO_CHUNK_SIZE = 1280 // 每次发送1280字节
        private const val SEND_INTERVAL = 40L // 每40ms发送一次
    }
    
    private val gson = Gson()
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(10, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()
    
    private var webSocket: WebSocket? = null
    private var audioRecord: AudioRecord? = null
    private var isRecording = false
    private var recordingJob: Job? = null

    // 结果构建器
    private val resultTextBuilder = StringBuilder()

    // 断句处理相关变量
    private var translatedText = ""  // 已翻译的文本
    
    // 回调接口
    interface AsrCallback {
        fun onConnected(sessionId: String)
        fun onResult(text: String, isIntermediate: Boolean = true)  // 增加source参数
        fun onError(code: String, message: String)
        fun onDisconnected()
    }
    
    private var callback: AsrCallback? = null
    
    /**
     * 开始语音识别
     */
    fun startRecognition(callback: AsrCallback) {
        this.callback = callback

        // 清空之前的结果
        resultTextBuilder.clear()
        translatedText = ""  // 清空已翻译文本

        // 先获取WebSocket URL
        getAsrToken { url ->
            if (url != null) {
                connectWebSocket(url)
            } else {
                callback.onError("TOKEN_ERROR", "Failed to get ASR Token")
            }
        }
    }
    
    /**
     * 停止语音识别
     */
    fun stopRecognition() {
        isRecording = false
        recordingJob?.cancel()
        
        // 发送结束信号
        webSocket?.let { ws ->
            val endMessage = JsonObject().apply {
                addProperty("end", true)
            }
            ws.send(gson.toJson(endMessage))
        }
        
        // 停止录音
        audioRecord?.apply {
            if (state == AudioRecord.STATE_INITIALIZED) {
                stop()
            }
            release()
        }
        audioRecord = null
        
        // 关闭WebSocket连接
        webSocket?.close(1000, "Normal closure")
        webSocket = null
    }
    
    /**
     * 获取ASR Token
     */
    private fun getAsrToken(onResult: (String?) -> Unit) {
        val request = Request.Builder()
            .url(TOKEN_URL)
            .post("".toRequestBody("application/json".toMediaType()))
            .build()
        
        httpClient.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e(TAG, "获取Token失败", e)
                onResult(null)
            }
            
            override fun onResponse(call: Call, response: Response) {
                response.use {
                    if (it.isSuccessful) {
                        val responseBody = it.body?.string()
                        Log.d(TAG, "Token响应: $responseBody")
                        
                        try {
                            val jsonObject = gson.fromJson(responseBody, JsonObject::class.java)
                            val url = jsonObject.get("url")?.asString
                            onResult(url)
                        } catch (e: Exception) {
                            Log.e(TAG, "解析Token响应失败", e)
                            onResult(null)
                        }
                    } else {
                        Log.e(TAG, "获取Token失败: ${it.code}")
                        onResult(null)
                    }
                }
            }
        })
    }
    
    /**
     * 连接WebSocket
     */
    private fun connectWebSocket(url: String) {
        val request = Request.Builder()
            .url(url)
            .build()
        
        webSocket = httpClient.newWebSocket(request, object : WebSocketListener() {
            override fun onOpen(webSocket: WebSocket, response: Response) {
                Log.d(TAG, "WebSocket连接成功")
            }
            
            override fun onMessage(webSocket: WebSocket, text: String) {
                Log.d(TAG, "收到消息: $text")
                handleWebSocketMessage(text)
            }
            
            override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
                Log.d(TAG, "WebSocket正在关闭: $code - $reason")
            }
            
            override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                Log.d(TAG, "WebSocket已关闭: $code - $reason")
                callback?.onDisconnected()
            }
            
            override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                Log.e(TAG, "WebSocket连接失败", t)
                callback?.onError("WEBSOCKET_ERROR", "WebSocket连接失败: ${t.message}")
            }
        })
    }
    
    /**
     * 处理WebSocket消息
     */
    private fun handleWebSocketMessage(message: String) {
        try {
            val jsonObject = gson.fromJson(message, JsonObject::class.java)
            val action = jsonObject.get("action")?.asString
            val code = jsonObject.get("code")?.asString
            val desc = jsonObject.get("desc")?.asString
            val sid = jsonObject.get("sid")?.asString
            
            when (action) {
                "started" -> {
                    if (code == "0") {
                        Log.d(TAG, "识别会话开始: $sid")
                        callback?.onConnected(sid ?: "")
                        startAudioRecording()
                    } else {
                        Log.e(TAG, "识别会话开始失败: $code - $desc")
                        callback?.onError(code ?: "UNKNOWN", desc ?: "未知错误")
                    }
                }
                "result" -> {
                    if (code == "0") {
                        val data = jsonObject.get("data")?.asString
                        if (data != null) {
                            parseRecognitionResult(data)
                        }
                    } else {
                        Log.e(TAG, "识别结果错误: $code - $desc")
                        callback?.onError(code ?: "UNKNOWN", desc ?: "识别错误")
                    }
                }
                "error" -> {
                    Log.e(TAG, "识别错误: $code - $desc")
                    callback?.onError(code ?: "UNKNOWN", desc ?: "识别错误")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "解析WebSocket消息失败", e)
        }
    }
    
    /**
     * 解析识别结果
     */
    private fun parseRecognitionResult(data: String) {
        parseAsrResult(data) { result, isIntermediate ->
            callback?.onResult(result, isIntermediate)
        }
    }

    /**
     * 按照Vue逻辑解析ASR结果，实现断句处理
     */
    private fun parseAsrResult(data: String, onResult: (String, Boolean) -> Unit) {
        try {
            val asrData = gson.fromJson(data, AsrResultData::class.java)
            var resultTextTemp = ""

            // 转写结果
            asrData.cn.st.rt.forEach { rt ->
                rt.ws.forEach { ws ->
                    ws.cw.forEach { cw ->
                        resultTextTemp += cw.w
                    }
                }
            }

            if (asrData.cn.st.type == "0") {
                // 最终识别结果
                resultTextBuilder.append(resultTextTemp)
                resultTextTemp = ""
            }

            // 讯飞ASR结果，要进行断句处理
            var text = resultTextBuilder.toString() + resultTextTemp
            Log.d(TAG, "总的ASR结果：$text")

            // 讯飞按照策略断句结果
            if (translatedText.isNotEmpty()) {
                val newText = getTextAfterResult(text, translatedText)
                text = newText
            }
            Log.d(TAG, "after: ${text.length}")

            val textBeforeFifthPunctuation = getTextBeforeFifthPunctuation(text)
            if (textBeforeFifthPunctuation != null) {
                translatedText += textBeforeFifthPunctuation
                Log.d(TAG, "xunfei speechRecognized: $textBeforeFifthPunctuation")
                // 通过回调返回最终结果（识别完成）
                onResult(textBeforeFifthPunctuation, false)  // isIntermediate = false, source = "xunfei"
            } else {
                Log.d(TAG, "xunfei speechRecognizing: $text")
                // 通过回调返回中间结果（识别中）
                onResult(text, true)  // isIntermediate = true, source = "xunfei"
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing ASR result", e)
        }
    }
    
    /**
     * 开始录音
     */
    private fun startAudioRecording() {
        val bufferSize = AudioRecord.getMinBufferSize(SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT) * BUFFER_SIZE_FACTOR
        
        try {
            audioRecord = AudioRecord(
                MediaRecorder.AudioSource.MIC,
                SAMPLE_RATE,
                CHANNEL_CONFIG,
                AUDIO_FORMAT,
                bufferSize
            )
            
            if (audioRecord?.state == AudioRecord.STATE_INITIALIZED) {
                audioRecord?.startRecording()
                isRecording = true
                
                recordingJob = CoroutineScope(Dispatchers.IO).launch {
                    sendAudioData()
                }
            } else {
                Log.e(TAG, "AudioRecord初始化失败")
                callback?.onError("AUDIO_ERROR", "录音初始化失败")
            }
        } catch (e: Exception) {
            Log.e(TAG, "开始录音失败", e)
            callback?.onError("AUDIO_ERROR", "开始录音失败: ${e.message}")
        }
    }
    
    /**
     * 发送音频数据
     */
    private suspend fun sendAudioData() {
        val buffer = ByteArray(AUDIO_CHUNK_SIZE)
        
        while (isRecording && audioRecord != null) {
            val bytesRead = audioRecord?.read(buffer, 0, buffer.size) ?: 0
            
            if (bytesRead > 0) {
                // 发送音频数据到WebSocket
                val audioData = buffer.copyOfRange(0, bytesRead)
                webSocket?.send(okio.ByteString.of(*audioData))
            }
            
            // 控制发送频率，每40ms发送一次
            delay(SEND_INTERVAL)
        }
    }

    /**
     * 获取文本中result之后的内容
     */
    private fun getTextAfterResult(newText: String, result: String): String {
        val index = newText.indexOf(result)
        return if (index != -1) {
            newText.substring(index + result.length)
        } else {
            ""
        }
    }

    /**
     * 如果超过10个标点，取前5个标点前的内容（包含第5个标点）
     */
    private fun getTextBeforeFifthPunctuation(text: String): String? {
        // 匹配常见中英文标点符号
        val punctuationRegex = Regex("[.,!?，。！？]")
        val punctuationIndexes = mutableListOf<Int>()

        // 收集所有标点位置
        punctuationRegex.findAll(text).forEach { match ->
            punctuationIndexes.add(match.range.first)
        }

        // 如果超过10个标点，取前5个标点前的内容（包含第5个标点）
        return if (punctuationIndexes.size > 10) {
            val fifthIndex = punctuationIndexes[4] // 第5个标点的index（0-based）
            text.substring(0, fifthIndex + 1)
        } else {
            null
        }
    }
}
