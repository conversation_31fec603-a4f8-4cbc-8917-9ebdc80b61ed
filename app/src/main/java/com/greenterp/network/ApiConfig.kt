package com.greenterp.network

/**
 * API配置常量
 */
object ApiConfig {
    // 基础URL
//    const val BASE_URL = "http://117.72.99.27:8128/"
    const val BASE_URL = "https://boothapi.gtmeeting.com/"
    
    // API端点
    object Endpoints {
        const val LOGIN = "conf/user/login"
        const val TRANSLATE = "api/translate"
        const val TRANSLATION_MODEL_LIST = "conf/translateModel/listAll"
        // 后续可以添加更多端点 ok
        // const val LOGOUT = "conf/user/logout"
        // const val USER_INFO = "conf/user/info"
    }
    
    // 请求超时配置（秒）
    const val CONNECT_TIMEOUT = 30L
    const val READ_TIMEOUT = 30L
    const val WRITE_TIMEOUT = 30L
}
