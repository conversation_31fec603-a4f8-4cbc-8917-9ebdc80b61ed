package com.greenterp

import android.util.Log
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.microsoft.cognitiveservices.speech.SpeechConfig
import com.microsoft.cognitiveservices.speech.SpeechRecognizer
import com.microsoft.cognitiveservices.speech.audio.AudioConfig
import com.microsoft.cognitiveservices.speech.AutoDetectSourceLanguageConfig
import com.microsoft.cognitiveservices.speech.PropertyId
import okhttp3.Call
import okhttp3.Callback
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import java.io.IOException
import java.util.concurrent.TimeUnit
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.ScheduledFuture

/**
 * Azure语音识别服务
 */
class AzureAsrService {
    
    companion object {
        private const val TAG = "AzureAsrService"
        private const val TOKEN_URL = "http://117.72.99.27:15005/getAzureToken"
    }
    
    private val gson = Gson()
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(10, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()
    
    private var speechConfig: SpeechConfig? = null
    private var speechRecognizer: SpeechRecognizer? = null
    private var isRecognizing = false
    private var isStoppedByUser = false // 标记是否由用户主动停止

    // 当前结果索引计数器
    private var currentResultIndex = 0

    // 线程池用于异步操作
    private val executorService = Executors.newCachedThreadPool()

    // 定时器用于token刷新
    private val tokenRefreshScheduler: ScheduledExecutorService = Executors.newSingleThreadScheduledExecutor()
    private var tokenRefreshTask: ScheduledFuture<*>? = null

    // 当前token和region信息
    private var currentToken: String? = null
    private var currentRegion: String? = null
    
    // 回调接口
    interface AsrCallback {
        fun onConnected(sessionId: String)
        fun onIntermediateResult(text: String, index: Int, languageCode:String)
        fun onFinalResult(text: String, index: Int, languageCode: String)
        fun onError(code: String, message: String)
        fun onDisconnected()
        fun onStopCompleted() // 新增停止完成回调
    }
    
    private var callback: AsrCallback? = null
    
    /**
     * 开始语音识别
     */
    fun startRecognition(languageCodes: List<String>, callback: AsrCallback) {
        this.callback = callback
        
        if (isRecognizing) {
            Log.w(TAG, "已经在识别中")
            return
        }

        // 重置结果索引计数器和用户停止标记
        currentResultIndex = 0
        isStoppedByUser = false
        
        // 先获取Azure Token
        getAzureToken { token, region ->
            if (token != null && region != null) {
                currentToken = token
                currentRegion = region
                initializeAzureSpeech(token, region, languageCodes)
                // 启动token刷新定时器
                startTokenRefreshTimer()
            } else {
                callback.onError("TOKEN_ERROR", "Failed to get Azure Token")
            }
        }
    }
    
    /**
     * 停止语音识别
     */
    fun stopRecognition() {
        if (!isRecognizing) {
            Log.w(TAG, "没有在识别中")
            return
        }

        isStoppedByUser = true // 标记为用户主动停止
        isRecognizing = false

        // 停止token刷新定时器
        stopTokenRefreshTimer()

        try {
            // 检查线程池状态
            if (executorService.isShutdown) {
                Log.w(TAG, "线程池已关闭，直接标记为停止")
                callback?.onStopCompleted()
                callback?.onDisconnected()
                return
            }

            // 异步停止，不阻塞当前线程
            executorService.submit {
                try {
                    speechRecognizer?.stopContinuousRecognitionAsync()?.get()
                    Log.d(TAG, "Azure语音识别已停止")
                } catch (e: Exception) {
                    Log.e(TAG, "停止Azure语音识别失败", e)
                } finally {
                    // 通知停止完成
                    callback?.onStopCompleted()
                    callback?.onDisconnected()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "停止Azure语音识别失败", e)
            callback?.onStopCompleted()
            callback?.onDisconnected()
        }
    }
    
    /**
     * 获取Azure Token
     */
    private fun getAzureToken(callback: (String?, String?) -> Unit) {
        val request = Request.Builder()
            .url(TOKEN_URL)
            .post("".toRequestBody("application/json".toMediaType()))
            .build()
        
        httpClient.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e(TAG, "获取Azure Token失败", e)
                callback(null, null)
            }
            
            override fun onResponse(call: Call, response: Response) {
                try {
                    val responseBody = response.body?.string()
                    if (response.isSuccessful && responseBody != null) {
                        val jsonObject = gson.fromJson(responseBody, JsonObject::class.java)
                        val token = jsonObject.get("token")?.asString
                        val region = jsonObject.get("region")?.asString
                        
                        Log.d(TAG, "获取Azure Token成功: region=$region")
                        callback(token, region)
                    } else {
                        Log.e(TAG, "获取Azure Token失败: ${response.code} - ${responseBody}")
                        callback(null, null)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "解析Azure Token响应失败", e)
                    callback(null, null)
                }
            }
        })
    }
    
    /**
     * 初始化Azure语音识别
     */
    private fun initializeAzureSpeech(token: String, region: String, languageCodes: List<String>) {
        try {
            // 使用Token创建SpeechConfig
            speechConfig = SpeechConfig.fromAuthorizationToken(token, region)

            // 设置多语言识别模式
            speechConfig?.setProperty(PropertyId.SpeechServiceConnection_LanguageIdMode, "Continuous")

            // 创建音频配置（使用默认麦克风）
            val audioConfig = AudioConfig.fromDefaultMicrophoneInput()

            // 创建自动语言检测配置
            val autoDetectConfig = AutoDetectSourceLanguageConfig.fromLanguages(languageCodes)

            // 创建语音识别器（支持多语言自动检测）
            speechRecognizer = SpeechRecognizer(speechConfig, autoDetectConfig, audioConfig)

            Log.d(TAG, "Azure多语言识别初始化完成，支持语言: ${languageCodes.joinToString(", ")}")

            // 设置事件监听器
            setupEventListeners()

            // 开始连续识别
            startContinuousRecognition()

        } catch (e: Exception) {
            Log.e(TAG, "初始化Azure语音识别失败", e)
            callback?.onError("INIT_ERROR", "初始化Azure语音识别失败: ${e.message}")
        }
    }
    
    /**
     * 设置事件监听器
     */
    private fun setupEventListeners() {
        speechRecognizer?.let { recognizer ->
            // 识别开始事件
            recognizer.sessionStarted.addEventListener { sender, e ->
                Log.d(TAG, "Azure语音识别会话开始: ${e.sessionId}")
                isRecognizing = true
                callback?.onConnected(e.sessionId)
            }
            
            // 识别中间结果事件（实时结果）
            recognizer.recognizing.addEventListener { sender, e ->
                val result = e.result.text
                val languageCode = try {
                    // 获取自动检测的语言代码
                    e.result.properties?.getProperty(PropertyId.SpeechServiceConnection_AutoDetectSourceLanguageResult) ?: "unknown"
                } catch (ex: Exception) {
                    Log.w(TAG, "无法获取中间结果语言代码: ${ex.message}")
                    "unknown"
                }

                Log.d(TAG, "Azure识别中间结果详情: result='$result', languageCode='$languageCode', properties=${e.result.properties}")

                if (result.isNotEmpty()) {
                    Log.d(TAG, "Azure识别中间结果: $result (语言: $languageCode)")
                    callback?.onIntermediateResult(result, currentResultIndex, languageCode)
                }
            }

            // 识别结果事件（最终结果）
            recognizer.recognized.addEventListener { sender, e ->
                val result = e.result.text
                val languageCode = try {
                    // 获取自动检测的语言代码
                    e.result.properties?.getProperty(PropertyId.SpeechServiceConnection_AutoDetectSourceLanguageResult) ?: "unknown"
                } catch (ex: Exception) {
                    Log.w(TAG, "无法获取最终结果语言代码: ${ex.message}")
                    "unknown"
                }

                Log.d(TAG, "Azure识别最终结果详情: result='$result', languageCode='$languageCode', properties=${e.result.properties}")

                if (result.isNotEmpty()) {
                    Log.d(TAG, "Azure识别最终结果: $result (语言: $languageCode)")
                    callback?.onFinalResult(result, currentResultIndex, languageCode)
                    currentResultIndex++ // 增加索引计数器
                }
            }
            
            // 识别错误事件
            recognizer.canceled.addEventListener { sender, e ->
                Log.e(TAG, "Azure语音识别取消: ${e.reason} - ${e.errorDetails}")
                isRecognizing = false

                // 只有在非用户主动停止时才报告错误
                if (!isStoppedByUser) {
                    callback?.onError("RECOGNITION_ERROR", "识别错误: ${e.errorDetails}")
                } else {
                    // 用户主动停止，正常断开连接
                    callback?.onDisconnected()
                }

                // 重置用户停止标记
                isStoppedByUser = false
            }
            
            // 会话结束事件
            recognizer.sessionStopped.addEventListener { sender, e ->
                Log.d(TAG, "Azure语音识别会话结束: ${e.sessionId}")
                isRecognizing = false
                callback?.onDisconnected()
            }
        }
    }
    
    /**
     * 开始连续识别
     */
    private fun startContinuousRecognition() {
        try {
            // 检查线程池状态
            if (executorService.isShutdown) {
                Log.e(TAG, "线程池已关闭，无法启动识别")
                callback?.onError("EXECUTOR_SHUTDOWN", "服务已关闭")
                return
            }

            // 异步启动，不阻塞当前线程
            executorService.submit {
                try {
                    speechRecognizer?.startContinuousRecognitionAsync()?.get()
                    Log.d(TAG, "Azure连续语音识别已启动")
                } catch (e: Exception) {
                    Log.e(TAG, "启动Azure连续语音识别失败", e)
                    // 启动失败时重置状态
                    isRecognizing = false
                    callback?.onError("START_ERROR", "启动识别失败: ${e.message}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "启动Azure连续语音识别失败", e)
            // 启动失败时重置状态
            isRecognizing = false
            callback?.onError("START_ERROR", "启动识别失败: ${e.message}")
        }
    }
    
    /**
     * 启动token刷新定时器
     */
    private fun startTokenRefreshTimer() {
        // 取消之前的定时器
        stopTokenRefreshTimer()

        // 每5分钟刷新一次token
        tokenRefreshTask = tokenRefreshScheduler.scheduleAtFixedRate({
            try {
                Log.d(TAG, "开始刷新Azure Token")
                refreshToken()
            } catch (e: Exception) {
                Log.e(TAG, "刷新Azure Token失败", e)
            }
        }, 5, 5, TimeUnit.MINUTES)

        Log.d(TAG, "Azure Token刷新定时器已启动")
    }

    /**
     * 停止token刷新定时器
     */
    private fun stopTokenRefreshTimer() {
        tokenRefreshTask?.let {
            it.cancel(false)
            tokenRefreshTask = null
            Log.d(TAG, "Azure Token刷新定时器已停止")
        }
    }

    /**
     * 刷新token
     */
    private fun refreshToken() {
        getAzureToken { token, region ->
            if (token != null && region != null) {
                currentToken = token
                currentRegion = region

                // 更新speechRecognizer的authorizationToken
                speechRecognizer?.let { recognizer ->
                    try {
                        // 使用反射或者重新设置authorizationToken
                        speechConfig?.authorizationToken = token
                        Log.d(TAG, "Azure Token刷新成功")
                    } catch (e: Exception) {
                        Log.e(TAG, "更新Azure Token失败", e)
                    }
                }
            } else {
                Log.e(TAG, "刷新Azure Token失败")
            }
        }
    }

    /**
     * 释放资源
     */
    fun release() {
        try {
            if (isRecognizing) {
                stopRecognition()
            }

            // 停止token刷新定时器
            stopTokenRefreshTimer()

            // 异步释放资源，避免阻塞主线程
            executorService.submit {
                try {
                    // 安全释放资源
                    speechRecognizer?.let {
                        it.close()
                        speechRecognizer = null
                    }
                    speechConfig?.let {
                        it.close()
                        speechConfig = null
                    }
                    Log.d(TAG, "Azure语音识别资源已释放")
                } catch (e: Exception) {
                    Log.e(TAG, "释放Azure语音识别资源失败", e)
                } finally {
                    // 在资源释放完成后关闭线程池
                    if (!executorService.isShutdown) {
                        executorService.shutdown()
                        try {
                            // 等待最多5秒让任务完成
                            if (!executorService.awaitTermination(5, java.util.concurrent.TimeUnit.SECONDS)) {
                                executorService.shutdownNow()
                            }
                        } catch (e: InterruptedException) {
                            executorService.shutdownNow()
                            Thread.currentThread().interrupt()
                        }
                    }

                    // 关闭token刷新定时器线程池
                    if (!tokenRefreshScheduler.isShutdown) {
                        tokenRefreshScheduler.shutdown()
                        try {
                            if (!tokenRefreshScheduler.awaitTermination(2, TimeUnit.SECONDS)) {
                                tokenRefreshScheduler.shutdownNow()
                            }
                        } catch (e: InterruptedException) {
                            tokenRefreshScheduler.shutdownNow()
                            Thread.currentThread().interrupt()
                        }
                    }
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "释放Azure语音识别服务失败", e)
        }
    }
}
