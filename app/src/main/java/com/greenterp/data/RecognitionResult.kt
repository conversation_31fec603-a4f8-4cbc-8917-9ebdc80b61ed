package com.greenterp.data

import java.util.UUID

/**
 * 识别结果数据类
 * 表示单个语音识别结果，包含转录文本和相关元数据
 */
data class RecognitionResult(
    val id: String = UUID.randomUUID().toString(),
    val transcript: String,
    val isIntermediate: Boolean = false, // 是否为中间结果
    val index: Int = -1, // 结果索引，用于替换中间结果
    val source: String = "unknown", // 识别服务来源，如 "xunfei", "azure" 等
    var languageCode: String = ""
)
