
package com.greenterp

import android.content.Context
import android.util.Log
import com.greenterp.data.RecognitionResult
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 统一的ASR管理器
 * 支持多种语音识别服务的切换和管理
 */
class UnifiedAsrManager(private val context: Context) {
    
    companion object {
        private const val TAG = "UnifiedAsrManager"
    }
    
    // 当前使用的ASR服务类型标识
    private var currentServiceType: String = AsrServiceType.ONLINE_1.type
    
    // ASR管理器实例
    private var xfeiAsrManager: XfeiAsrManager? = null
    private var azureAsrManager: AzureAsrManager? = null
    
    // 统一的状态流
    private val _isRecording = MutableStateFlow(false)
    val isRecording: StateFlow<Boolean> = _isRecording.asStateFlow()

    private val _recognitionResults = MutableStateFlow<List<RecognitionResult>>(emptyList())
    val recognitionResults: StateFlow<List<RecognitionResult>> = _recognitionResults.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    private val _isConnected = MutableStateFlow(false)
    val isConnected: StateFlow<Boolean> = _isConnected.asStateFlow()

    private val _currentSessionId = MutableStateFlow<String?>(null)
    val currentSessionId: StateFlow<String?> = _currentSessionId.asStateFlow()

    // 连接状态：idle, connecting, connected, error
    private val _connectionState = MutableStateFlow("idle")
    val connectionState: StateFlow<String> = _connectionState.asStateFlow()
    
    // 当前语言设置
    private var primaryLanguage: String = "English-United States"
    private var secondaryLanguage: String = "Chinese-Mandarin"
    private var tertiaryLanguage: String = "Auto"
    private var currentLanguageIsPrimary: Boolean = true
    private var primaryServiceDisplayName: String = "Online-1"
    private var secondaryServiceDisplayName: String = "Online-1"
    private var tertiaryServiceDisplayName: String = "Online-1"
    private var isAutoLanguageEnabled: Boolean = false
    private var currentLanguageType: LanguageType = LanguageType.PRIMARY

    // 协程作用域和任务
    private val coroutineScope = CoroutineScope(Dispatchers.Main)
    private var observeJob: Job? = null
    
    init {
        // 初始化默认的ASR管理器
        initializeCurrentManager()
    }
    
    /**
     * 设置语音识别服务配置（二语言模式）
     */
    fun setServiceConfiguration(
        primaryLang: String,
        secondaryLang: String,
        primaryService: String,
        secondaryService: String,
        isPrimary: Boolean
    ) {
        setThreeLanguageConfiguration(
            primaryLang = primaryLang,
            secondaryLang = secondaryLang,
            tertiaryLang = "Auto",
            primaryService = primaryService,
            secondaryService = secondaryService,
            tertiaryService = "Online-1",
            currentLanguageType = if (isPrimary) LanguageType.PRIMARY else LanguageType.SECONDARY,
            isAutoEnabled = false
        )
    }

    /**
     * 设置三语言识别服务配置
     */
    fun setThreeLanguageConfiguration(
        primaryLang: String,
        secondaryLang: String,
        tertiaryLang: String,
        primaryService: String,
        secondaryService: String,
        tertiaryService: String,
        currentLanguageType: LanguageType,
        isAutoEnabled: Boolean
    ) {
        val oldLanguageType = this.currentLanguageType
        val oldServiceType = currentServiceType

        // 更新所有语言配置
        primaryLanguage = primaryLang
        secondaryLanguage = secondaryLang
        tertiaryLanguage = tertiaryLang
        primaryServiceDisplayName = primaryService
        secondaryServiceDisplayName = secondaryService
        tertiaryServiceDisplayName = tertiaryService
        this.currentLanguageType = currentLanguageType
        isAutoLanguageEnabled = isAutoEnabled

        // 更新兼容性变量
        currentLanguageIsPrimary = when (currentLanguageType) {
            LanguageType.PRIMARY -> true
            LanguageType.SECONDARY -> false
            LanguageType.TERTIARY -> currentLanguageIsPrimary // Auto模式保持当前状态
        }

        // 更新当前服务类型标识
        val currentServiceDisplayName = when (currentLanguageType) {
            LanguageType.PRIMARY -> primaryService
            LanguageType.SECONDARY -> secondaryService
            LanguageType.TERTIARY -> tertiaryService
        }
        currentServiceType = LanguageConfig.getServiceTypeIdentifier(currentServiceDisplayName)

        Log.d(TAG, "设置三语言服务配置: 主语言=$primaryLang($primaryService), 次语言=$secondaryLang($secondaryService), 第三语言=$tertiaryLang($tertiaryService), 当前语言类型=$currentLanguageType, 服务类型=$currentServiceType, Auto启用=$isAutoEnabled")

        // 检查是否需要重新初始化（语言类型切换或服务类型变化）
        val needReinitialize = (oldLanguageType != currentLanguageType) || (oldServiceType != currentServiceType)

        if (needReinitialize) {
            // 如果正在录音或连接中，先停止
            val wasRecording = _isRecording.value
            val wasConnecting = _connectionState.value == "connecting"

            if (wasRecording || wasConnecting) {
                Log.d(TAG, "语言切换时停止当前识别")
                stopRecognition()
                _connectionState.value = "idle"
            }

            // 重新初始化管理器
            initializeCurrentManager()
        }

        // 设置语言
        updateCurrentLanguage()
    }
    
    /**
     * 切换语言（已弃用 - 现在由MainActivity的LaunchedEffect统一处理）
     * 保留此方法以保持API兼容性，但实际逻辑已移至setServiceConfiguration
     */
    @Deprecated("语言切换现在由MainActivity的LaunchedEffect统一处理")
    fun toggleLanguage() {
        // 此方法现在为空，实际的语言切换逻辑在setServiceConfiguration中处理
        Log.d(TAG, "toggleLanguage() 被调用，但逻辑已移至setServiceConfiguration")
    }
    
    /**
     * 初始化当前的ASR管理器
     */
    private fun initializeCurrentManager() {
        // 释放之前的管理器
        releaseManagers()

        when (currentServiceType) {
            AsrServiceType.ONLINE_1.type -> {
                // Online-1 使用Azure实现
                azureAsrManager = AzureAsrManager(context)
                observeAzureManager()
            }
            AsrServiceType.ONLINE_ZH_EN.type -> {
                // Online-ZH/EN only 使用讯飞实现
                xfeiAsrManager = XfeiAsrManager(context)
                observeXfeiManager()
            }
            else -> {
                // 默认使用Online-1 (Azure实现)
                azureAsrManager = AzureAsrManager(context)
                observeAzureManager()
            }
        }

        Log.d(TAG, "初始化ASR管理器: $currentServiceType")
    }
    
    /**
     * 更新当前语言设置
     */
    private fun updateCurrentLanguage() {
        when (currentLanguageType) {
            LanguageType.TERTIARY -> {
                // Auto语言模式
                updateAutoLanguageSettings()
            }
            LanguageType.PRIMARY, LanguageType.SECONDARY -> {
                // 非Auto模式（Primary/Secondary）
                updateNonAutoLanguageSettings()
            }
        }
    }

    /**
     * 更新Auto语言模式的设置
     */
    private fun updateAutoLanguageSettings() {
        Log.d(TAG, "更新Auto语言设置, 第三语言服务=$tertiaryServiceDisplayName")

        when (tertiaryServiceDisplayName) {
            "Online-ZH/EN only" -> {
                // Auto + Online-ZH/EN only：使用讯飞ASR，保持原有逻辑
                val languageCode = "zh-CN" // 讯飞默认使用中文
                Log.d(TAG, "Auto模式 + 讯飞ASR: $languageCode")
                xfeiAsrManager?.setLanguage(languageCode)
            }
            "Online-1" -> {
                // Auto + Online-1：使用Azure多语言识别，传入primary+secondary语言列表
                val primaryCode = LanguageConfig.getCurrentLanguageCode(primaryLanguage, primaryServiceDisplayName) ?: "en-US"
                val secondaryCode = LanguageConfig.getCurrentLanguageCode(secondaryLanguage, secondaryServiceDisplayName) ?: "zh-CN"
                val languageCodes = listOf(primaryCode, secondaryCode).distinct()

                Log.d(TAG, "Auto模式 + Azure ASR多语言: primary=$primaryCode, secondary=$secondaryCode -> ${languageCodes.joinToString(", ")}")
                azureAsrManager?.setLanguages(languageCodes)
            }
            else -> {
                // 默认使用Azure多语言识别
                val primaryCode = LanguageConfig.getCurrentLanguageCode(primaryLanguage, primaryServiceDisplayName) ?: "en-US"
                val secondaryCode = LanguageConfig.getCurrentLanguageCode(secondaryLanguage, secondaryServiceDisplayName) ?: "zh-CN"
                val languageCodes = listOf(primaryCode, secondaryCode).distinct()

                Log.d(TAG, "Auto模式 + Azure ASR多语言(默认): ${languageCodes.joinToString(", ")}")
                azureAsrManager?.setLanguages(languageCodes)
            }
        }
    }

    /**
     * 更新非Auto语言模式的设置
     */
    private fun updateNonAutoLanguageSettings() {
        val currentLang = when (currentLanguageType) {
            LanguageType.PRIMARY -> primaryLanguage
            LanguageType.SECONDARY -> secondaryLanguage
            else -> primaryLanguage // 不应该到这里
        }

        val currentServiceDisplayName = when (currentLanguageType) {
            LanguageType.PRIMARY -> primaryServiceDisplayName
            LanguageType.SECONDARY -> secondaryServiceDisplayName
            else -> primaryServiceDisplayName // 不应该到这里
        }

        val languageCode = LanguageConfig.getCurrentLanguageCode(currentLang, currentServiceDisplayName)

        Log.d(TAG, "更新非Auto语言设置: $currentLang -> $languageCode, 服务类型=$currentServiceType")

        when (currentServiceType) {
            AsrServiceType.ONLINE_1.type -> {
                // Primary/Secondary + Online-1：使用Azure多语言识别，但只传入单个语言
                Log.d(TAG, "非Auto模式 + Azure ASR单语言: $languageCode")
                azureAsrManager?.setLanguage(languageCode ?: "en-US")
            }
            AsrServiceType.ONLINE_ZH_EN.type -> {
                // Primary/Secondary + Online-ZH/EN only：使用讯飞ASR单语言
                Log.d(TAG, "非Auto模式 + 讯飞ASR单语言: $languageCode")
                xfeiAsrManager?.setLanguage(languageCode ?: "en-US")
            }
            else -> {
                // 默认使用Azure单语言识别
                Log.d(TAG, "非Auto模式 + Azure ASR单语言(默认): $languageCode")
                azureAsrManager?.setLanguage(languageCode ?: "en-US")
            }
        }
    }
    
    /**
     * 观察讯飞ASR管理器状态
     */
    private fun observeXfeiManager() {
        observeJob?.cancel()
        xfeiAsrManager?.let { manager ->
            observeJob = coroutineScope.launch {
                launch {
                    manager.isRecording.collect {
                        _isRecording.value = it
                        if (it) {
                            _connectionState.value = "connected"
                        }
                    }
                }
                launch {
                    manager.recognitionResults.collect { _recognitionResults.value = it }
                }
                launch {
                    manager.errorMessage.collect {
                        _errorMessage.value = it
                        if (it != null) {
                            _connectionState.value = "error"
                        }
                    }
                }
                launch {
                    manager.isConnected.collect { _isConnected.value = it }
                }
                launch {
                    manager.currentSessionId.collect { _currentSessionId.value = it }
                }
            }
        }
    }

    /**
     * 观察Azure ASR管理器状态
     */
    private fun observeAzureManager() {
        observeJob?.cancel()
        azureAsrManager?.let { manager ->
            observeJob = coroutineScope.launch {
                launch {
                    manager.isRecording.collect {
                        _isRecording.value = it
                        if (it) {
                            _connectionState.value = "connected"
                        }
                    }
                }
                launch {
                    manager.recognitionResults.collect { _recognitionResults.value = it }
                }
                launch {
                    manager.errorMessage.collect {
                        _errorMessage.value = it
                        if (it != null) {
                            _connectionState.value = "error"
                        }
                    }
                }
                launch {
                    manager.isConnected.collect { _isConnected.value = it }
                }
                launch {
                    manager.currentSessionId.collect { _currentSessionId.value = it }
                }
            }
        }
    }
    
    /**
     * 检查录音权限
     */
    fun hasRecordPermission(): Boolean {
        return when (currentServiceType) {
            AsrServiceType.ONLINE_1.type -> azureAsrManager?.hasRecordPermission() ?: false
            AsrServiceType.ONLINE_ZH_EN.type -> xfeiAsrManager?.hasRecordPermission() ?: false
            else -> azureAsrManager?.hasRecordPermission() ?: false
        }
    }

    /**
     * 开始语音识别
     */
    fun startRecognition(): Boolean {
        // 如果已经在连接或录音中，不允许重复启动
        if (_connectionState.value == "connecting" || _isRecording.value) {
            Log.w(TAG, "已经在连接或录音中，无法重复启动")
            return false
        }

        updateCurrentLanguage()

        // 设置连接状态为连接中
        _connectionState.value = "connecting"
        _errorMessage.value = null

        return when (currentServiceType) {
            AsrServiceType.ONLINE_1.type -> {
                azureAsrManager?.startRecognition() ?: false
            }
            AsrServiceType.ONLINE_ZH_EN.type -> {
                xfeiAsrManager?.startRecognition() ?: false
            }
            else -> {
                azureAsrManager?.startRecognition() ?: false
            }
        }
    }

    /**
     * 停止语音识别
     */
    fun stopRecognition() {
        // 重置连接状态
        _connectionState.value = "idle"

        when (currentServiceType) {
            AsrServiceType.ONLINE_1.type -> azureAsrManager?.stopRecognition()
            AsrServiceType.ONLINE_ZH_EN.type -> xfeiAsrManager?.stopRecognition()
            else -> azureAsrManager?.stopRecognition()
        }
    }

    /**
     * 设置语言（支持多语言）
     */
    fun setLanguages(languageCodes: List<String>) {
        when (currentServiceType) {
            AsrServiceType.ONLINE_1.type -> azureAsrManager?.setLanguages(languageCodes)
            AsrServiceType.ONLINE_ZH_EN.type -> {
                // 讯飞服务只支持单语言，取第一个语言
                if (languageCodes.isNotEmpty()) {
                    xfeiAsrManager?.setLanguage(languageCodes.first())
                }
            }
            else -> azureAsrManager?.setLanguages(languageCodes)
        }
    }

    /**
     * 设置单个语言（向后兼容）
     */
    fun setLanguage(languageCode: String) {
        when (currentServiceType) {
            AsrServiceType.ONLINE_1.type -> azureAsrManager?.setLanguage(languageCode)
            AsrServiceType.ONLINE_ZH_EN.type -> xfeiAsrManager?.setLanguage(languageCode)
            else -> azureAsrManager?.setLanguage(languageCode)
        }
    }

    /**
     * 清空识别结果
     */
    fun clearResults() {
        when (currentServiceType) {
            AsrServiceType.ONLINE_1.type -> azureAsrManager?.clearResults()
            AsrServiceType.ONLINE_ZH_EN.type -> xfeiAsrManager?.clearResults()
            else -> azureAsrManager?.clearResults()
        }
    }

    /**
     * 清空错误信息
     */
    fun clearError() {
        when (currentServiceType) {
            AsrServiceType.ONLINE_1.type -> azureAsrManager?.clearError()
            AsrServiceType.ONLINE_ZH_EN.type -> xfeiAsrManager?.clearError()
            else -> azureAsrManager?.clearError()
        }
    }
    
    /**
     * 获取当前识别结果
     */
    fun getCurrentResults(): List<RecognitionResult> {
        return when (currentServiceType) {
            AsrServiceType.ONLINE_1.type -> azureAsrManager?.recognitionResults?.value ?: emptyList()
            AsrServiceType.ONLINE_ZH_EN.type -> xfeiAsrManager?.recognitionResults?.value ?: emptyList()
            else -> azureAsrManager?.recognitionResults?.value ?: emptyList()
        }
    }
    
    /**
     * 释放所有管理器资源
     */
    private fun releaseManagers() {
        observeJob?.cancel()
        observeJob = null
        azureAsrManager?.release()
        azureAsrManager = null
        xfeiAsrManager = null
    }

    /**
     * 释放资源
     */
    fun release() {
        releaseManagers()
        Log.d(TAG, "统一ASR管理器资源已释放")
    }
}
